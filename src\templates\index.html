<div class="angular-reservation-box">
    <uib-tabset active="reservationCtrl.selectedTab" justified="true">
        <uib-tab index="0">
            <uib-tab-heading>
                <span class="glyphicon glyphicon-calendar" aria-hidden="true" class="angular-reservation-icon-size"></span>
                <h5 ng-if="reservationCtrl.secondTabLocked">{{"date" | translate}}</h5>
                <h5 ng-if="!reservationCtrl.secondTabLocked">{{reservationCtrl.selectedDate | date: reservationCtrl.dateFormat}}</h5>
            </uib-tab-heading>

            <div ng-include="'loader.html'" class="text-center" style="min-height: 250px" ng-if="reservationCtrl.loader"></div>

            <div ng-include="reservationCtrl.datepickerTemplate" ng-if="!reservationCtrl.loader"></div>
        </uib-tab>

        <uib-tab index="1" disable="reservationCtrl.secondTabLocked">
            <uib-tab-heading>
                <span class="glyphicon glyphicon-time" aria-hidden="true" class="angular-reservation-icon-size"></span>
                <h5 ng-if="reservationCtrl.thirdTabLocked">{{"time" | translate}}</h5>
                <h5 ng-if="!reservationCtrl.thirdTabLocked">{{reservationCtrl.selectedHour}}</h5>
            </uib-tab-heading>

            <div ng-include="'loader.html'" class="text-center" style="min-height: 250px" ng-if="reservationCtrl.loader"></div>

            <div class="angular-reservation-availableHour" ng-if="!reservationCtrl.loader && reservationCtrl.availableHours.length > 0">
                <div ng-include="reservationCtrl.availableHoursTemplate"></div>
            </div>

            <div ng-if="!reservationCtrl.loader && reservationCtrl.availableHours.length == 0">
                <div ng-include="reservationCtrl.noAvailableHoursTemplate"></div>
            </div>
        </uib-tab>

        <uib-tab index="2" disable="reservationCtrl.thirdTabLocked">
            <uib-tab-heading>
                <span class="glyphicon glyphicon-user" aria-hidden="true" class="angular-reservation-icon-size"></span>
                <h5>{{"client" | translate}}</h5>
            </uib-tab-heading>

            <form class="form-horizontal" name="reserveForm" novalidate
                  ng-submit="reserveForm.$valid && reservationCtrl.reserve(reservationCtrl.selectedDate, reservationCtrl.selectedHour, reservationCtrl.userData)">
                <div ng-include="'loader.html'" class="text-center" style="min-height: 250px" ng-if="reservationCtrl.loader"></div>

                <fieldset ng-if="!reservationCtrl.loader">
                    <div ng-include="reservationCtrl.clientFormTemplate"></div>
                </fieldset>
            </form>
        </uib-tab>
    </uib-tabset>
</div>
