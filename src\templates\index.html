<div class="angular-reservation-box fade-in-up">
    <!-- Header with branding -->
    <div class="reservation-header">
        <h2 class="reservation-title">
            <i class="fas fa-calendar-check"></i>
            Book Your Appointment
        </h2>
        <p class="reservation-subtitle">Select your preferred date and time</p>
    </div>

    <uib-tabset active="reservationCtrl.selectedTab" justified="true" class="glass-tabs">
        <uib-tab index="0" class="glass-tab">
            <uib-tab-heading>
                <div class="tab-content-wrapper">
                    <i class="fas fa-calendar-alt angular-reservation-icon-size"></i>
                    <div class="tab-text">
                        <h5 ng-if="reservationCtrl.secondTabLocked">{{"date" | translate}}</h5>
                        <h5 ng-if="!reservationCtrl.secondTabLocked" class="selected-value">
                            {{reservationCtrl.selectedDate | date: reservationCtrl.dateFormat}}
                        </h5>
                        <small ng-if="!reservationCtrl.secondTabLocked" class="step-indicator">Step 1 Complete</small>
                    </div>
                </div>
            </uib-tab-heading>

            <div class="tab-content-area">
                <div ng-include="'loader.html'" class="text-center angular-reservation-loader-min-height" ng-if="reservationCtrl.loader"></div>

                <div ng-include="reservationCtrl.datepickerTemplate" ng-if="!reservationCtrl.loader" class="fade-in-up"></div>
            </div>
        </uib-tab>

        <uib-tab index="1" disable="reservationCtrl.secondTabLocked" class="glass-tab">
            <uib-tab-heading>
                <div class="tab-content-wrapper">
                    <i class="fas fa-clock angular-reservation-icon-size"></i>
                    <div class="tab-text">
                        <h5 ng-if="reservationCtrl.thirdTabLocked">{{"time" | translate}}</h5>
                        <h5 ng-if="!reservationCtrl.thirdTabLocked" class="selected-value">{{reservationCtrl.selectedHour}}</h5>
                        <small ng-if="!reservationCtrl.thirdTabLocked" class="step-indicator">Step 2 Complete</small>
                    </div>
                </div>
            </uib-tab-heading>

            <div class="tab-content-area">
                <div ng-include="'loader.html'" class="text-center angular-reservation-loader-min-height" ng-if="reservationCtrl.loader"></div>

                <div ng-if="!reservationCtrl.loader && reservationCtrl.availableHours.length > 0" class="fade-in-up">
                    <div class="time-selection-header">
                        <h4>Available Time Slots</h4>
                        <p>Choose your preferred appointment time</p>
                    </div>
                    <div class="angular-reservation-availableHour">
                        <div ng-include="reservationCtrl.availableHoursTemplate"></div>
                    </div>
                </div>

                <div ng-if="!reservationCtrl.loader && reservationCtrl.availableHours.length == 0" class="fade-in-up">
                    <div ng-include="reservationCtrl.noAvailableHoursTemplate"></div>
                </div>
            </div>
        </uib-tab>

        <uib-tab index="2" disable="reservationCtrl.thirdTabLocked" class="glass-tab">
            <uib-tab-heading>
                <div class="tab-content-wrapper">
                    <i class="fas fa-user angular-reservation-icon-size"></i>
                    <div class="tab-text">
                        <h5>{{"client" | translate}}</h5>
                        <small class="step-indicator">Final Step</small>
                    </div>
                </div>
            </uib-tab-heading>

            <div class="tab-content-area">
                <form class="form-horizontal reservation-form" name="reserveForm" novalidate
                      ng-submit="reserveForm.$valid && reservationCtrl.reserve(reservationCtrl.selectedDate, reservationCtrl.selectedHour, reservationCtrl.userData)">
                    <div ng-include="'loader.html'" class="text-center angular-reservation-loader-min-height" ng-if="reservationCtrl.loader"></div>

                    <fieldset ng-if="!reservationCtrl.loader" class="fade-in-up">
                        <div class="client-form-header">
                            <h4>Your Information</h4>
                            <p>Please provide your details to complete the booking</p>
                        </div>
                        <div ng-include="reservationCtrl.clientFormTemplate"></div>
                    </fieldset>
                </form>
            </div>
        </uib-tab>
    </uib-tabset>

    <!-- Progress indicator -->
    <div class="progress-indicator">
        <div class="progress-step" ng-class="{'active': reservationCtrl.selectedTab >= 0, 'completed': !reservationCtrl.secondTabLocked}">
            <span class="step-number">1</span>
            <span class="step-label">Date</span>
        </div>
        <div class="progress-line" ng-class="{'completed': !reservationCtrl.secondTabLocked}"></div>
        <div class="progress-step" ng-class="{'active': reservationCtrl.selectedTab >= 1, 'completed': !reservationCtrl.thirdTabLocked}">
            <span class="step-number">2</span>
            <span class="step-label">Time</span>
        </div>
        <div class="progress-line" ng-class="{'completed': !reservationCtrl.thirdTabLocked}"></div>
        <div class="progress-step" ng-class="{'active': reservationCtrl.selectedTab >= 2}">
            <span class="step-number">3</span>
            <span class="step-label">Details</span>
        </div>
    </div>
</div>
