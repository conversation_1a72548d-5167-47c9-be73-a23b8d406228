/*UI Bootstrap fix @see https://angular-ui.github.io/bootstrap/#/getting_started */
.nav, .pagination, .carousel, .panel-title a { cursor: pointer; }

/* Glassmorphism Base Styles */
:root {
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-backdrop: blur(8px);
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --glass-text: rgba(255, 255, 255, 0.9);
    --glass-text-secondary: rgba(255, 255, 255, 0.7);
}

/* Main Container with Glassmorphism */
.angular-reservation-box {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    padding: 2rem;
    margin: 1rem;
    position: relative;
    overflow: hidden;
}

.angular-reservation-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.1;
    z-index: -1;
    border-radius: 20px;
}

/* Enhanced Icons */
.angular-reservation-icon-size {
    font-size: 24px;
    color: var(--glass-text);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

/* Glassmorphism Tabs */
.nav-tabs {
    border: none;
    background: transparent;
}

.nav-tabs > li > a {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 15px 15px 0 0;
    color: var(--glass-text);
    margin-right: 5px;
    transition: all 0.3s ease;
    padding: 15px 20px;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    background: var(--success-gradient);
    border: 1px solid var(--glass-border);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.nav-tabs > li > a:hover {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid var(--glass-border);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Tab Content */
.tab-content {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 0 20px 20px 20px;
    padding: 2rem;
    min-height: 400px;
    position: relative;
}

/* Selection Styles */
.angular-reservation-selected {
    background: var(--success-gradient) !important;
    color: white !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.angular-reservation-selected:hover {
    background: var(--success-gradient) !important;
    color: white !important;
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
    transform: scale(1.08);
}

/* Datepicker Glassmorphism */
.angular-reservation-datepicker {
    width: 100%;
    max-width: 350px;
    margin: auto;
    margin-top: 2em;
    margin-bottom: 2em;
}

.angular-reservation-datepicker .uib-datepicker {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem;
    box-shadow: var(--glass-shadow);
}

.angular-reservation-datepicker .uib-datepicker table {
    width: 100%;
}

.angular-reservation-datepicker .uib-datepicker .btn {
    background: transparent;
    border: 1px solid var(--glass-border);
    color: var(--glass-text);
    border-radius: 8px;
    margin: 2px;
    transition: all 0.3s ease;
}

.angular-reservation-datepicker .uib-datepicker .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Available Hours */
.angular-reservation-availableHour {
    margin: 2em 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.angular-reservation-availableHour .btn {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    color: var(--glass-text);
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.angular-reservation-availableHour .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--secondary-gradient);
    transition: left 0.3s ease;
    z-index: -1;
}

.angular-reservation-availableHour .btn:hover::before {
    left: 0;
}

.angular-reservation-availableHour .btn:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

/* No Available Hours */
.angular-reservation-noAvailableHours {
    margin: 2em;
    padding: 3rem;
    text-align: center;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    color: var(--glass-text-secondary);
}

.angular-reservation-noAvailableHours h3 {
    color: var(--glass-text);
    margin-bottom: 1rem;
}

/* Client Form */
.angular-reservation-clientForm {
    margin-top: 2em;
    margin-bottom: 2em;
}

.angular-reservation-clientForm .form-group {
    margin-bottom: 1.5rem;
}

.angular-reservation-clientForm .form-control {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 10px;
    color: var(--glass-text);
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.angular-reservation-clientForm .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #4facfe;
    box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
    outline: none;
}

.angular-reservation-clientForm .form-control::placeholder {
    color: var(--glass-text-secondary);
}

.angular-reservation-clientForm label {
    color: var(--glass-text);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Buttons */
.btn-primary {
    background: var(--success-gradient);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 30px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.btn-secondary {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    color: var(--glass-text);
    padding: 12px 30px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Loader */
.angular-reservation-loader-min-height {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .angular-reservation-box {
        margin: 0.5rem;
        padding: 1rem;
        border-radius: 15px;
    }

    .angular-reservation-availableHour {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }

    .nav-tabs > li > a {
        padding: 10px 15px;
        font-size: 14px;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Enhanced Header Styles */
.reservation-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
}

.reservation-title {
    color: var(--glass-text);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.reservation-title i {
    margin-right: 0.5rem;
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.reservation-subtitle {
    color: var(--glass-text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* Enhanced Tab Styles */
.glass-tabs .nav-tabs {
    border: none;
    margin-bottom: 0;
}

.tab-content-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.tab-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.tab-text h5 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
}

.selected-value {
    color: #4facfe !important;
    font-weight: 600 !important;
}

.step-indicator {
    font-size: 0.75rem;
    color: var(--glass-text-secondary);
    margin-top: 0.25rem;
}

.tab-content-area {
    padding: 1.5rem;
    min-height: 350px;
}

/* Progress Indicator */
.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2rem;
    padding: 1rem 0;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--glass-text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: var(--secondary-gradient);
    border-color: #f093fb;
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
}

.progress-step.completed .step-number {
    background: var(--success-gradient);
    border-color: #4facfe;
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.step-label {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--glass-text-secondary);
    font-weight: 500;
}

.progress-step.active .step-label,
.progress-step.completed .step-label {
    color: var(--glass-text);
}

.progress-line {
    width: 60px;
    height: 2px;
    background: var(--glass-border);
    margin: 0 1rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.progress-line.completed {
    background: var(--success-gradient);
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

/* Section Headers */
.time-selection-header,
.client-form-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.time-selection-header h4,
.client-form-header h4 {
    color: var(--glass-text);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.time-selection-header p,
.client-form-header p {
    color: var(--glass-text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Enhanced Form Styles */
.reservation-form {
    max-width: 500px;
    margin: 0 auto;
}

.reservation-form .form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.reservation-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--glass-text);
    font-weight: 500;
    font-size: 0.9rem;
}

.reservation-form .form-control {
    width: 100%;
    padding: 12px 15px;
    border-radius: 10px;
    border: 1px solid var(--glass-border);
    background: var(--glass-bg);
    -webkit-backdrop-filter: var(--glass-backdrop);
    backdrop-filter: var(--glass-backdrop);
    color: var(--glass-text);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.reservation-form .form-control:focus {
    outline: none;
    border-color: #4facfe;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.reservation-form .form-control::placeholder {
    color: var(--glass-text-secondary);
}

/* Enhanced Button Styles */
.btn-reservation {
    background: var(--success-gradient);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 12px 30px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin-top: 1rem;
}

.btn-reservation:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.btn-reservation:active {
    transform: translateY(0);
}

.btn-reservation:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .reservation-title {
        font-size: 1.5rem;
    }

    .tab-content-wrapper {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .tab-text {
        align-items: center;
        text-align: center;
    }

    .progress-indicator {
        margin-top: 1rem;
    }

    .progress-line {
        width: 40px;
        margin: 0 0.5rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }

    .step-label {
        font-size: 0.7rem;
    }

    .tab-content-area {
        padding: 1rem;
        min-height: 300px;
    }
}