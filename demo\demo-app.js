/**
 * Demo Application for ReserveNow Pro
 * Showcases the enhanced Angular Reservation system with glassmorphism design
 */

// Demo Application Module
angular.module('reservationDemo', ['hm.reservation'])
    .config(['$translateProvider', 'reservationConfigProvider', function($translateProvider, reservationConfigProvider) {
        
        // Configure translations
        $translateProvider.translations('en', {
            'date': 'Select Date',
            'time': 'Choose Time',
            'client': 'Your Details',
            'name': 'Full Name',
            'email': 'Email Address',
            'phone': 'Phone Number',
            'comments': 'Special Requests',
            'reserve': 'Complete Booking',
            'cancel': 'Cancel',
            'confirm': 'Confirm Reservation',
            'confirmation_message': 'Please confirm your reservation for {{name}} on {{selectedDate}} at {{selectedHour}}.',
            'loading': 'Loading...',
            'no_available_hours': 'No available time slots for this date. Please select another date.',
            'success_message': 'Your reservation has been confirmed!',
            'error_message': 'Sorry, there was an error processing your reservation. Please try again.'
        });
        
        $translateProvider.preferredLanguage('en');
        
        // Configure reservation system
        var config = {
            getAvailableDatesFromAPI: false,
            getAvailableHoursAPIUrl: "demo-api/available-hours",
            reserveAPIUrl: "demo-api/reserve",
            dateFormat: "MMMM dd, yyyy",
            language: "en",
            showConfirmationModal: true,
            datepickerTemplate: "datepicker.html",
            availableHoursTemplate: "availableHours.html",
            noAvailableHoursTemplate: "noAvailableHours.html",
            clientFormTemplate: "clientForm.html",
            confirmationModalTemplate: "confirmationModal.html"
        };
        
        reservationConfigProvider.set(config);
    }])
    
    .controller('DemoController', ['$scope', '$http', '$timeout', function($scope, $http, $timeout) {
        var vm = this;
        
        // Demo configuration
        vm.config = {
            // Override API URLs for demo
            getAvailableHoursAPIUrl: null, // Will use mock data
            reserveAPIUrl: null // Will use mock reservation
        };
        
        // Initialize demo
        vm.init = function() {
            console.log('ReserveNow Pro Demo initialized');
            
            // Add fade-in animation to elements
            $timeout(function() {
                var elements = document.querySelectorAll('.fade-in-up');
                elements.forEach(function(el, index) {
                    $timeout(function() {
                        el.style.opacity = '0';
                        el.style.transform = 'translateY(30px)';
                        el.style.transition = 'all 0.6s ease';
                        
                        $timeout(function() {
                            el.style.opacity = '1';
                            el.style.transform = 'translateY(0)';
                        }, 100);
                    }, index * 200);
                });
            }, 500);
        };
        
        vm.init();
    }])
    
    // Mock API Service for Demo
    .service('mockAPIService', ['$q', '$timeout', function($q, $timeout) {
        
        // Mock available hours data
        var mockHours = [
            '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
            '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
        ];
        
        // Simulate API delay
        function simulateAPICall(data, delay) {
            var deferred = $q.defer();
            $timeout(function() {
                deferred.resolve({
                    data: data
                });
            }, delay || 1000);
            return deferred.promise;
        }
        
        this.getAvailableHours = function(date) {
            // Simulate some hours being unavailable
            var availableHours = mockHours.filter(function(hour, index) {
                return Math.random() > 0.3; // 70% chance of being available
            });
            
            return simulateAPICall({
                status: 'SUCCESS',
                message: 'Available hours retrieved successfully',
                availableHours: availableHours
            });
        };
        
        this.makeReservation = function(reservationData) {
            // Simulate reservation processing
            return simulateAPICall({
                status: 'SUCCESS',
                message: 'Reservation confirmed successfully! You will receive a confirmation email shortly.',
                reservationId: 'RES-' + Date.now()
            }, 2000);
        };
    }])
    
    // Override the reservation factory for demo purposes
    .factory('reservationAPIFactory', ['$http', '$q', 'reservationConfig', 'mockAPIService', 
        function($http, $q, reservationConfig, mockAPIService) {
        
        var reservationAPI = {};
        
        // Status and message properties
        reservationAPI.status = "";
        reservationAPI.message = "";
        reservationAPI.availableHours = [];
        reservationAPI.availableDates = [];
        
        // Get available hours (using mock data for demo)
        reservationAPI.getAvailableHours = function(date) {
            return mockAPIService.getAvailableHours(date).then(function(response) {
                reservationAPI.status = response.data.status;
                reservationAPI.message = response.data.message;
                reservationAPI.availableHours = response.data.availableHours;
            });
        };
        
        // Make reservation (using mock API for demo)
        reservationAPI.reserve = function(params) {
            return mockAPIService.makeReservation(params).then(function(response) {
                reservationAPI.status = response.data.status;
                reservationAPI.message = response.data.message;
                
                // Show success notification
                if (response.data.status === 'SUCCESS') {
                    showNotification('Success!', response.data.message, 'success');
                }
            }).catch(function(error) {
                reservationAPI.status = 'ERROR';
                reservationAPI.message = 'Failed to process reservation. Please try again.';
                showNotification('Error', reservationAPI.message, 'error');
            });
        };
        
        // Error management
        reservationAPI.errorManagement = function(status) {
            switch (status) {
                case 500:
                    reservationAPI.status = "SERVER_ERROR";
                    reservationAPI.message = "Server error occurred. Please try again later.";
                    break;
                default:
                    reservationAPI.status = "CONNECTION_ERROR";
                    reservationAPI.message = "Connection error. Please check your internet connection.";
                    break;
            }
            showNotification('Error', reservationAPI.message, 'error');
        };
        
        return reservationAPI;
    }])
    
    // Enhanced reservation service with demo features
    .service('reservationService', ['$q', '$filter', '$uibModal', 'reservationConfig', 
        function($q, $filter, $uibModal, reservationConfig) {
        
        // Success callback with enhanced UX
        this.onSuccessfulReserve = function(status, message, selectedDate, selectedHour, userData) {
            console.log("Reservation successful:", {
                status: status,
                message: message,
                date: selectedDate,
                hour: selectedHour,
                user: userData
            });
            
            // Add success animation
            var reservationBox = document.querySelector('.angular-reservation-box');
            if (reservationBox) {
                reservationBox.classList.add('pulse');
                setTimeout(function() {
                    reservationBox.classList.remove('pulse');
                }, 2000);
            }
        };
        
        // Error callback with user-friendly messaging
        this.onErrorReserve = function(status, message, selectedDate, selectedHour, userData) {
            console.log("Reservation error:", {
                status: status,
                message: message,
                date: selectedDate,
                hour: selectedHour,
                user: userData
            });
        };
        
        // Before reserve callback with enhanced confirmation modal
        this.onBeforeReserve = function(selectedDate, selectedHour, userData) {
            var deferred = $q.defer();
            
            if (reservationConfig.showConfirmationModal) {
                var modalInstance = $uibModal.open({
                    templateUrl: 'confirmationModal.html',
                    size: 'md',
                    backdrop: 'static',
                    windowClass: 'glass-modal',
                    controller: ['$uibModalInstance', 'selectedDate', 'selectedHour', 'userData', 
                        function($uibModalInstance, selectedDate, selectedHour, userData) {
                            var vm = this;
                            
                            vm.selectedDate = $filter('date')(selectedDate, reservationConfig.dateFormat);
                            vm.selectedHour = selectedHour;
                            vm.userData = userData;
                            
                            vm.confirm = function() {
                                $uibModalInstance.close();
                            };
                            
                            vm.cancel = function() {
                                $uibModalInstance.dismiss('cancel');
                            };
                        }],
                    controllerAs: 'confirmationCtrl',
                    resolve: {
                        selectedDate: function() { return selectedDate; },
                        selectedHour: function() { return selectedHour; },
                        userData: function() { return userData; }
                    }
                });
                
                modalInstance.result.then(function() {
                    deferred.resolve();
                }, function() {
                    deferred.reject();
                });
            } else {
                deferred.resolve();
            }
            
            return deferred.promise;
        };
        
        // Other required callbacks
        this.onCompletedGetAvailableHours = function(status, message, selectedDate) {
            console.log("Available hours loaded:", status, message);
        };
        
        this.onSuccessfulGetAvailableHours = function(status, message, selectedDate, availableHours) {
            console.log("Available hours success:", availableHours.length, "slots found");
        };
        
        this.onErrorGetAvailableHours = function(status, message, selectedDate) {
            console.log("Available hours error:", status, message);
        };
        
        this.onBeforeGetAvailableHours = function(selectedDate) {
            var deferred = $q.defer();
            deferred.resolve();
            return deferred.promise;
        };
        
        this.onCompletedReserve = function(status, message, selectedDate, selectedHour, userData) {
            console.log("Reserve completed:", status);
        };
    }]);

// Utility function for notifications
function showNotification(title, message, type) {
    // Create notification element
    var notification = document.createElement('div');
    notification.className = 'notification notification-' + type;
    notification.innerHTML = `
        <div class="notification-content">
            <h4>${title}</h4>
            <p>${message}</p>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
        color: white;
        z-index: 9999;
        max-width: 300px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    if (type === 'success') {
        notification.style.borderLeft = '4px solid #4facfe';
    } else if (type === 'error') {
        notification.style.borderLeft = '4px solid #f5576c';
    }
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(function() {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove
    setTimeout(function() {
        notification.style.transform = 'translateX(100%)';
        setTimeout(function() {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}
