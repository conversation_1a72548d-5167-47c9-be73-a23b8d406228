<!DOCTYPE html>
<html lang="en" ng-app="reservationDemo">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReserveNow Pro - Modern Reservation Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Angular Reservation CSS -->
    <link rel="stylesheet" href="../src/css/style.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
        }
        
        .hero-section {
            padding: 4rem 0;
            text-align: center;
            color: white;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            font-weight: 300;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            color: white;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .pricing-section {
            margin: 4rem 0;
            text-align: center;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .pricing-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            color: white;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .pricing-card.featured {
            border: 2px solid #4facfe;
            transform: scale(1.05);
        }
        
        .pricing-card:hover {
            transform: translateY(-5px) scale(1.02);
        }
        
        .pricing-card.featured:hover {
            transform: translateY(-5px) scale(1.07);
        }
        
        .price {
            font-size: 3rem;
            font-weight: 700;
            margin: 1rem 0;
        }
        
        .btn-glass {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            color: white;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 1rem 0.5rem;
        }
        
        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }
        
        .btn-primary-glass {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
        }
        
        .btn-primary-glass:hover {
            background: linear-gradient(135deg, #00f2fe 0%, #4facfe 100%);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .features-grid,
            .pricing-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="container">
                <h1 class="hero-title">ReserveNow Pro</h1>
                <p class="hero-subtitle">The Future of Reservation Management</p>
                <p class="lead">Experience the most advanced booking platform with glassmorphism design, real-time availability, and seamless user experience.</p>
                
                <div class="features-grid">
                    <div class="feature-card fade-in-up">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h4>Smart Scheduling</h4>
                        <p>AI-powered availability management with real-time synchronization across all platforms.</p>
                    </div>
                    
                    <div class="feature-card fade-in-up">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>Mobile First</h4>
                        <p>Responsive glassmorphism design that works perfectly on all devices and screen sizes.</p>
                    </div>
                    
                    <div class="feature-card fade-in-up">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>Analytics Dashboard</h4>
                        <p>Comprehensive insights and reporting to optimize your booking performance and revenue.</p>
                    </div>
                    
                    <div class="feature-card fade-in-up">
                        <div class="feature-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <h4>Payment Integration</h4>
                        <p>Secure payment processing with support for all major payment methods and currencies.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Demo Section -->
        <div class="demo-container">
            <div class="text-center mb-4">
                <h2 style="color: white; font-weight: 600;">Interactive Demo</h2>
                <p style="color: rgba(255, 255, 255, 0.8);">Try our reservation system with the new glassmorphism design</p>
            </div>
            
            <!-- Angular Reservation Component -->
            <div ng-controller="DemoController as demo">
                <reservation config="demo.config"></reservation>
            </div>
        </div>
        
        <!-- Pricing Section -->
        <div class="pricing-section">
            <div class="container">
                <h2 style="color: white; font-weight: 600; margin-bottom: 1rem;">Choose Your Plan</h2>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 3rem;">Flexible pricing for businesses of all sizes</p>
                
                <div class="pricing-grid">
                    <div class="pricing-card">
                        <h3>Starter</h3>
                        <div class="price">$29<small>/month</small></div>
                        <ul style="list-style: none; padding: 0;">
                            <li>✓ Up to 100 bookings/month</li>
                            <li>✓ Basic customization</li>
                            <li>✓ Email support</li>
                            <li>✓ Mobile responsive</li>
                        </ul>
                        <a href="#" class="btn-glass">Get Started</a>
                    </div>
                    
                    <div class="pricing-card featured">
                        <h3>Professional</h3>
                        <div class="price">$99<small>/month</small></div>
                        <ul style="list-style: none; padding: 0;">
                            <li>✓ Unlimited bookings</li>
                            <li>✓ Advanced customization</li>
                            <li>✓ Priority support</li>
                            <li>✓ Analytics dashboard</li>
                            <li>✓ Payment integration</li>
                        </ul>
                        <a href="#" class="btn-glass btn-primary-glass">Most Popular</a>
                    </div>
                    
                    <div class="pricing-card">
                        <h3>Enterprise</h3>
                        <div class="price">$299<small>/month</small></div>
                        <ul style="list-style: none; padding: 0;">
                            <li>✓ White-label solution</li>
                            <li>✓ Custom integrations</li>
                            <li>✓ Dedicated support</li>
                            <li>✓ Multi-location</li>
                            <li>✓ API access</li>
                        </ul>
                        <a href="#" class="btn-glass">Contact Sales</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AngularJS and Dependencies -->
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/2.5.0/ui-bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/angular-ui-bootstrap/2.5.0/ui-bootstrap-tpls.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/angular-translate/2.18.1/angular-translate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/angular-messages/1.8.2/angular-messages.min.js"></script>
    
    <!-- Angular Reservation Module -->
    <script src="../dist/angular-reservation.js"></script>
    
    <!-- Demo App -->
    <script src="demo-app.js"></script>
</body>
</html>
