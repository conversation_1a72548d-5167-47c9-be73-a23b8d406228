!function(){angular.module("hm.reservation",["ui.bootstrap","pascalprecht.translate","ngMessages"])}(),function(){function e(){var e={getAvailableDatesFromAPI:!1,getAvailableDatesAPIUrl:"http://localhost:8080/availableDates",getAvailableHoursAPIUrl:"http://localhost:8080/availableHours",reserveAPIUrl:"http://localhost:8080/reserve",dateFormat:"yyyy-MM-dd",language:"en",showConfirmationModal:!0,datepickerTemplate:"datepicker.html",availableHoursTemplate:"availableHours.html",noAvailableHoursTemplate:"noAvailableHours.html",clientFormTemplate:"clientForm.html",confirmationModalTemplate:"confirmationModal.html"};return{$get:function(){return e},set:function(n){angular.extend(e,n)}}}angular.module("hm.reservation").provider("reservationConfig",[e])}(),function(){function e(e,n,a,t,r,s){function o(){v.loader=!0,t.getAvailableDates().then(function(){v.loader=!1;var e=v.availableDatesStatus=t.status,n=v.availableDatesMessage=t.message;s.onCompletedGetAvailableDates(e,n),"SUCCESS"==e?(v.availableDates=t.availableDates,s.onSuccessfulGetAvailableDates(e,n,v.availableDates),v.availableDates.length>0&&(v.selectedDate=new Date(v.availableDates[0]))):s.onErrorGetAvailableDates(e,n)})}function i(e){return v.availableDates.indexOf(e.toISOString().substr(0,10))!==-1}function l(e){var n=e.date,a=e.mode;return"day"===a&&!i(n)}function c(e){s.onBeforeGetAvailableHours(e).then(function(){d(e)},function(){console.log("onBeforeGetAvailableHours: Rejected promise")})}function d(e){var a=n("date")(e,v.dateFormat),r={selectedDate:a};t.getAvailableHours(r).then(function(){v.loader=!1;var n=v.availableHoursStatus=t.status,a=v.availableHoursMessage=t.message;s.onCompletedGetAvailableHours(n,a,e),"SUCCESS"==n?(v.availableHours=t.availableHours,s.onSuccessfulGetAvailableHours(n,a,e,v.availableHours)):s.onErrorGetAvailableHours(n,a,e)})}function u(e,n,a){s.onBeforeReserve(e,n,a).then(function(){m(e,n,a)},function(){console.log("onBeforeReserve: Rejected promise")})}function m(e,a,r){v.loader=!0;var o=n("date")(e,v.dateFormat),i={selectedDate:o,selectedHour:a,userData:r};t.reserve(i).then(function(){v.loader=!1;var n=v.reservationStatus=t.status,o=v.reservationMessage=t.message;s.onCompletedReserve(n,o,e,a,r),"SUCCESS"==n?s.onSuccessfulReserve(n,o,e,a,r):s.onErrorReserve(n,o,e,a,r)})}var v=this;v.selectedTab=0,v.secondTabLocked=!0,v.thirdTabLocked=!0,v.selectedDate=new Date,v.selectedHour="",v.userData={},v.loader=!1,v.config=r||{},e.config&&(v.config=angular.extend(v.config,e.config)),v.getAvailableDatesFromAPI=v.config.getAvailableDatesFromAPI,v.dateFormat=v.config.dateFormat,v.datepickerTemplate=v.config.datepickerTemplate,v.availableHoursTemplate=v.config.availableHoursTemplate,v.noAvailableHoursTemplate=v.config.noAvailableHoursTemplate,v.clientFormTemplate=v.config.clientFormTemplate,v.datepickerOptions=e.datepickerOptions||{},a.use(v.config.language),v.getAvailableDatesFromAPI&&(v.availableDates=[],o(),v.datepickerOptions.dateDisabled=l),v.onSelectDate=function(e){v.selectedDate=e,v.secondTabLocked=!1,v.selectedTab=1,c(e),v.loader=!0},v.selectHour=function(e){v.thirdTabLocked=!1,v.selectedHour=e,v.selectedTab=2},v.reserve=function(e,n,a){u(e,n,a)}}angular.module("hm.reservation").controller("ReservationCtrl",["$scope","$filter","$translate","reservationAPIFactory","reservationConfig","reservationService",e])}(),function(){angular.module("hm.reservation").directive("reservation",[function(){return{restrict:"E",scope:{datepickerOptions:"=",config:"="},controller:"ReservationCtrl",controllerAs:"reservationCtrl",templateUrl:"index.html"}}])}(),function(){function e(e,n){function a(){o.status="",o.message="",o.availableHours=""}function t(e){e.hasOwnProperty("status")||console.error("Get available hours response should have a 'status' key"),e.hasOwnProperty("message")||console.error("Get available hours response should have a 'message' key"),e.hasOwnProperty("availableDates")||console.error("Get available hours response should have a 'availableDates' key")}function r(e){e.hasOwnProperty("status")||console.error("Get available hours response should have a 'status' key"),e.hasOwnProperty("message")||console.error("Get available hours response should have a 'message' key"),e.hasOwnProperty("availableHours")||console.error("Get available hours response should have a 'availableHours' key")}function s(e){e.hasOwnProperty("status")||console.error("Reserve response should have a 'status' key"),e.hasOwnProperty("message")||console.error("Reserve response should have a 'message' key")}var o={};return o.status="",o.message="",o.availableHours=[],o.availableDates=[],o.getAvailableDates=function(){return e({method:"GET",url:n.getAvailableDatesAPIUrl,responseType:"json"}).then(function(e){console.log(e.data),t(e.data),o.status=e.data.status,o.message=e.data.message,o.availableDates=e.data.availableDates},function(e){o.errorManagement(e.status)})},o.getAvailableHours=function(a){return e({method:"GET",params:a,url:n.getAvailableHoursAPIUrl,responseType:"json"}).then(function(e){console.log(e.data),r(e.data),o.status=e.data.status,o.message=e.data.message,o.availableHours=e.data.availableHours},function(e){o.errorManagement(e.status)})},o.reserve=function(a){return e({method:"POST",data:a,url:n.reserveAPIUrl,responseType:"json"}).then(function(e){console.log(e.data),s(e.data),o.status=e.data.status,o.message=e.data.message},function(e){o.errorManagement(e.status)})},o.errorManagement=function(e){switch(a(),e){case 500:o.status="SERVER_ERROR";break;default:o.status="CONNECTION_ERROR"}},o}angular.module("hm.reservation").factory("reservationAPIFactory",["$http","reservationConfig",e])}(),function(){function e(e,n,a,t){function r(e,r,o,i){var l=a.open({templateUrl:t.confirmationModalTemplate,size:"sm",controller:["selectedDate","selectedHour","userData",s],controllerAs:"confirmationModalCtrl",resolve:{selectedDate:function(){return n("date")(r,t.dateFormat)},selectedHour:function(){return o},userData:function(){return i}}});l.result.then(function(){console.log("Accepted"),e.resolve()},function(){console.log("Cancelled"),e.reject()})}function s(e,n,a){var t=this;t.selectedDate=e,t.selectedHour=n,t.userData=a,t.translationParams={name:a.name,selectedDate:e,selectedHour:n}}this.onCompletedGetAvailableDates=function(e,n){console.log("Executing completed get available dates callback")},this.onSuccessfulGetAvailableDates=function(e,n,a){console.log("Executing successful get available dates callback")},this.onErrorGetAvailableDates=function(e,n){console.log("Executing error get available dates callback")},this.onBeforeGetAvailableHours=function(n){console.log("Executing before get available hours callback");var a=e.defer();return a.resolve(),a.promise},this.onCompletedGetAvailableHours=function(e,n,a){console.log("Executing completed get available hours callback")},this.onSuccessfulGetAvailableHours=function(e,n,a,t){console.log("Executing successful get available hours callback")},this.onErrorGetAvailableHours=function(e,n,a){console.log("Executing error get available hours callback")},this.onBeforeReserve=function(n,a,s){console.log("Executing before reserve callback");var o=e.defer();return t.showConfirmationModal?r(o,n,a,s):o.resolve(),o.promise},this.onCompletedReserve=function(e,n,a,t,r){console.log("Executing completed reserve callback")},this.onSuccessfulReserve=function(e,n,a,t,r){console.log("Executing successful reserve callback")},this.onErrorReserve=function(e,n,a,t,r){console.log("Executing error reserve callback")}}angular.module("hm.reservation").service("reservationService",["$q","$filter","$uibModal","reservationConfig",e])}(),function(){"use strict";angular.module("hm.reservation").config(["$translateProvider",function(e){e.translations("en",{date:"Date",time:"Time",client:"Client",name:"Name",save:"Save",cancel:"Cancel",select:"Select",phone:"Phone",email:"Email",required:"This field is required",minLength:"Minimum length of {{minLength}} is required",maxLength:"Maximum length of {{maxLength}} is required",invalidCharacters:"Not allowed characters",invalidPhone:"Invalid phone number",invalidEmail:"Invalid email address",reserve:"Reserve",confirmOK:"Yes, reserve",confirmCancel:"No, cancel",confirmTitle:"Confirm reservation",confirmText:"{{name}}, Are you sure you want to reserve date {{selectedDate}} at {{selectedHour}}?.",noAvailableHours:"There are not available hours for selected date, please select another date"}),e.translations("es",{date:"Fecha",time:"Hora",client:"Cliente",name:"Nombre",save:"Guardar",cancel:"Cancelar",select:"Seleccionar",phone:"Teléfono",email:"Email",required:"Este campo no puede estar vacío",minLength:"El número mínimo de carácteres es {{minLength}}",maxLength:"El número máximo de carácteres es {{maxLength}}",invalidCharacters:"Caracteres no permitidos",invalidPhone:"Número de teléfono no válido",invalidEmail:"Email no válido",reserve:"Reservar",confirmOK:"Sí, reservar",confirmCancel:"No, cancelar",confirmTitle:"Confirmar reserva",confirmText:"{{name}}, ¿Estás seguro de que deseas reservar el día {{selectedDate}} a las {{selectedHour}}?.",noAvailableHours:"No hay horas disponibles para la fecha seleccionada, por favor selecciona otra fecha"}),e.registerAvailableLanguageKeys(["es","en"],{"es_*":"es","en_*":"en"}),e.determinePreferredLanguage(),e.useSanitizeValueStrategy("escaped")}])}(),angular.module("hm.reservation").run(["$templateCache",function(e){e.put("availableHours.html",'<a class="list-group-item" href="" ng-repeat="item in reservationCtrl.availableHours" ng-click="reservationCtrl.selectHour(item)"\n   ng-class="{\'angular-reservation-selected\': reservationCtrl.selectedHour == item}">\n    <span>{{item}}</span>\n</a>'),e.put("clientForm.html",'<div class="col-md-12 angular-reservation-clientForm">\n    <div class="form-group col-md-12">\n        <label class="col-md-3 control-label" for="name">{{"name" | translate}}</label>\n        <div class="col-md-9">\n            <div class="input-group">\n            <span class="input-group-addon">\n                <span class="glyphicon glyphicon-user" aria-hidden="true" style="font-size: 14px"></span>\n            </span>\n\n                <input id="name" name="name" class="form-control" placeholder="{{\'name\' | translate}}" type="text" ng-model="reservationCtrl.userData.name"\n                       autofocus="true" ng-pattern="/^[\\w\\s\\-\\x7f-\\xff]*$/" ng-minlength="2" ng-maxlength="100" required>\n            </div>\n\n            <div class="help-block" ng-messages="reserveForm.name.$error" ng-if="reserveForm.$submitted">\n                <p ng-message="minlength" class="text-danger">{{"minLength" | translate: \'{minLength: "2"}\'}}</p>\n                <p ng-message="maxlength" class="text-danger">{{"maxLength" | translate: \'{maxLength: "100"}\'}}</p>\n                <p ng-message="pattern" class="text-danger">{{"invalidCharacters" | translate}}</p>\n                <p ng-message="required" class="text-danger">{{"required" | translate}}</p>\n            </div>\n        </div>\n    </div>\n\n    <div class="form-group col-md-12">\n        <label class="col-md-3 control-label" for="phone">{{"phone" | translate}}</label>\n        <div class="col-md-9">\n            <div class="input-group">\n            <span class="input-group-addon">\n                <span class="glyphicon glyphicon-earphone" aria-hidden="true" style="font-size: 14px"></span>\n            </span>\n\n                <input id="phone" name="phone" class="form-control" placeholder="{{\'phone\' | translate}}" type="tel" ng-model="reservationCtrl.userData.phone"\n                       ng-pattern="/^[0-9]*$/" ng-minlength="5" ng-maxlength="15" required>\n            </div>\n\n            <div class="help-block" ng-messages="reserveForm.phone.$error" ng-if="reserveForm.$submitted">\n                <p ng-message="minlength" class="text-danger">{{"minLength" | translate: \'{minLength: "5"}\'}}</p>\n                <p ng-message="maxlength" class="text-danger">{{"maxLength" | translate: \'{maxLength: "15"}\'}}</p>\n                <p ng-message="pattern" class="text-danger">{{"invalidPhone" | translate}}</p>\n                <p ng-message="required" class="text-danger">{{"required" | translate}}</p>\n            </div>\n        </div>\n    </div>\n\n    <div class="form-group col-md-12">\n        <label class="col-md-3 control-label" for="email">{{"email" | translate}}</label>\n        <div class="col-md-9">\n            <div class="input-group">\n            <span class="input-group-addon">\n                <span class="glyphicon glyphicon-envelope" aria-hidden="true" style="font-size: 14px"></span>\n            </span>\n\n                <input id="email" name="email" class="form-control" placeholder="{{\'email\' | translate}}" type="text" ng-model="reservationCtrl.userData.email"\n                       ng-pattern="/[\\w|.|-]*@\\w*\\.[\\w|.]*/" required>\n            </div>\n\n            <div class="help-block" ng-messages="reserveForm.email.$error" ng-if="reserveForm.$submitted">\n                <p ng-message="pattern" class="text-danger">{{"invalidEmail" | translate}}</p>\n                <p ng-message="required" class="text-danger">{{"required" | translate}}</p>\n            </div>\n        </div>\n    </div>\n\n    <div class="col-md-12">\n        <button id="reserve" type="submit" name="reserve" class="btn btn-success pull-right">{{"reserve" | translate}}</button>\n    </div>\n\n    <div class="col-md-12">\n        <div uib-alert class="alert-success text-center" ng-if="reservationCtrl.reservationStatus == \'SUCCESS\'" style="margin-top: 1em">\n            <span>Success!</span>\n            <p ng-if="reservationCtrl.reservationMessage">{{reservationCtrl.reservationMessage}}</p>\n        </div>\n\n        <div uib-alert class="alertt-danger text-center" ng-if="reservationCtrl.reservationStatus == \'ERROR\'" style="margin-top: 1em">\n            <span>Error!</span>\n            <p ng-if="reservationCtrl.reservationMessage">{{reservationCtrl.reservationMessage}}</p>\n        </div>\n    </div>\n</div>'),e.put("confirmationModal.html",'<div class="modal-header">\n    <h3 class="modal-title">{{"confirmTitle" | translate}}</h3>\n</div>\n\n<div class="modal-body">\n    <h5>{{"confirmText" | translate : confirmationModalCtrl.translationParams}}</h5>\n\n    <div ng-repeat="(key, value) in confirmationModalCtrl.userData track by $index">\n        <label class="control-label">{{key | translate}}</label>\n\n        <h5>{{value}}</h5>\n    </div>\n</div>\n\n<div class="modal-footer">\n    <button class="btn btn-danger" type="button" ng-click="$dismiss()">{{"confirmCancel" | translate}}</button>\n    <button class="btn btn-success" type="button" ng-click="$close()">{{"confirmOK" | translate}}</button>\n</div>'),e.put("datepicker.html",'<div uib-datepicker class="angular-reservation-datepicker" ng-model="reservationCtrl.selectedDate" datepicker-options="reservationCtrl.datepickerOptions"\n     ng-change="reservationCtrl.onSelectDate(reservationCtrl.selectedDate)"></div>'),e.put("index.html",'<div class="angular-reservation-box">\n    <uib-tabset active="reservationCtrl.selectedTab" justified="true">\n        <uib-tab index="0">\n            <uib-tab-heading>\n                <span class="glyphicon glyphicon-calendar" aria-hidden="true" class="angular-reservation-icon-size"></span>\n                <h5 ng-if="reservationCtrl.secondTabLocked">{{"date" | translate}}</h5>\n                <h5 ng-if="!reservationCtrl.secondTabLocked">{{reservationCtrl.selectedDate | date: reservationCtrl.dateFormat}}</h5>\n            </uib-tab-heading>\n\n            <div ng-include="\'loader.html\'" class="text-center" style="min-height: 250px" ng-if="reservationCtrl.loader"></div>\n\n            <div ng-include="reservationCtrl.datepickerTemplate" ng-if="!reservationCtrl.loader"></div>\n        </uib-tab>\n\n        <uib-tab index="1" disable="reservationCtrl.secondTabLocked">\n            <uib-tab-heading>\n                <span class="glyphicon glyphicon-time" aria-hidden="true" class="angular-reservation-icon-size"></span>\n                <h5 ng-if="reservationCtrl.thirdTabLocked">{{"time" | translate}}</h5>\n                <h5 ng-if="!reservationCtrl.thirdTabLocked">{{reservationCtrl.selectedHour}}</h5>\n            </uib-tab-heading>\n\n            <div ng-include="\'loader.html\'" class="text-center" style="min-height: 250px" ng-if="reservationCtrl.loader"></div>\n\n            <div class="angular-reservation-availableHour" ng-if="!reservationCtrl.loader && reservationCtrl.availableHours.length > 0">\n                <div ng-include="reservationCtrl.availableHoursTemplate"></div>\n            </div>\n\n            <div ng-if="!reservationCtrl.loader && reservationCtrl.availableHours.length == 0">\n                <div ng-include="reservationCtrl.noAvailableHoursTemplate"></div>\n            </div>\n        </uib-tab>\n\n        <uib-tab index="2" disable="reservationCtrl.thirdTabLocked">\n            <uib-tab-heading>\n                <span class="glyphicon glyphicon-user" aria-hidden="true" class="angular-reservation-icon-size"></span>\n                <h5>{{"client" | translate}}</h5>\n            </uib-tab-heading>\n\n            <form class="form-horizontal" name="reserveForm" novalidate\n                  ng-submit="reserveForm.$valid && reservationCtrl.reserve(reservationCtrl.selectedDate, reservationCtrl.selectedHour, reservationCtrl.userData)">\n                <div ng-include="\'loader.html\'" class="text-center" style="min-height: 250px" ng-if="reservationCtrl.loader"></div>\n\n                <fieldset ng-if="!reservationCtrl.loader">\n                    <div ng-include="reservationCtrl.clientFormTemplate"></div>\n                </fieldset>\n            </form>\n        </uib-tab>\n    </uib-tabset>\n</div>\n'),e.put("loader.html",'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1" width="50px" height="50px" viewBox="0 0 28 28">\n    <!-- 28= RADIUS*2 + STROKEWIDTH -->\n\n    <title>Material design circular activity spinner with CSS3 animation</title>\n    <style type="text/css">\n        /**************************/\n        /* STYLES FOR THE SPINNER */\n        /**************************/\n\n        /*\n         * Constants:\n         *      RADIUS      = 12.5\n         *      STROKEWIDTH = 3\n         *      ARCSIZE     = 270 degrees (amount of circle the arc takes up)\n         *      ARCTIME     = 1333ms (time it takes to expand and contract arc)\n         *      ARCSTARTROT = 216 degrees (how much the start location of the arc\n         *                                should rotate each time, 216 gives us a\n         *                                5 pointed star shape (it\'s 360/5 * 2).\n         *                                For a 7 pointed star, we might do\n         *                                360/7 * 3 = 154.286)\n         *\n         *      SHRINK_TIME = 400ms\n         */\n\n        .qp-circular-loader {\n            width:28px;  /* 2*RADIUS + STROKEWIDTH */\n            height:28px; /* 2*RADIUS + STROKEWIDTH */\n        }\n        .qp-circular-loader-path {\n            stroke-dasharray: 58.9;  /* 2*RADIUS*PI * ARCSIZE/360 */\n            stroke-dashoffset: 58.9; /* 2*RADIUS*PI * ARCSIZE/360 */\n            /* hides things initially */\n        }\n\n        /* SVG elements seem to have a different default origin */\n        .qp-circular-loader, .qp-circular-loader * {\n            -webkit-transform-origin: 50% 50%;\n            -moz-transform-origin: 50% 50%;\n        }\n\n        /* Rotating the whole thing */\n        @-webkit-keyframes rotate {\n            from {-webkit-transform: rotate(0deg);}\n            to {-webkit-transform: rotate(360deg);}\n        }\n        @-moz-keyframes rotate {\n            from {-webkit-transform: rotate(0deg);}\n            to {-webkit-transform: rotate(360deg);}\n        }\n        .qp-circular-loader {\n            -webkit-animation-name: rotate;\n            -webkit-animation-duration: 1568.63ms; /* 360 * ARCTIME / (ARCSTARTROT + (360-ARCSIZE)) */\n            -webkit-animation-iteration-count: infinite;\n            -webkit-animation-timing-function: linear;\n            -moz-animation-name: rotate;\n            -moz-animation-duration: 1568.63ms; /* 360 * ARCTIME / (ARCSTARTROT + (360-ARCSIZE)) */\n            -moz-animation-iteration-count: infinite;\n            -moz-animation-timing-function: linear;\n        }\n\n        /* Filling and unfilling the arc */\n        @-webkit-keyframes fillunfill {\n            from {\n                stroke-dashoffset: 58.8 /* 2*RADIUS*PI * ARCSIZE/360 - 0.1 */\n                /* 0.1 a bit of a magic constant here */\n            }\n            50% {\n                stroke-dashoffset: 0;\n            }\n            to {\n                stroke-dashoffset: -58.4 /* -(2*RADIUS*PI * ARCSIZE/360 - 0.5) */\n                /* 0.5 a bit of a magic constant here */\n            }\n        }\n        @-moz-keyframes fillunfill {\n            from {\n                stroke-dashoffset: 58.8 /* 2*RADIUS*PI * ARCSIZE/360 - 0.1 */\n                /* 0.1 a bit of a magic constant here */\n            }\n            50% {\n                stroke-dashoffset: 0;\n            }\n            to {\n                stroke-dashoffset: -58.4 /* -(2*RADIUS*PI * ARCSIZE/360 - 0.5) */\n                /* 0.5 a bit of a magic constant here */\n            }\n        }\n        @-webkit-keyframes rot {\n            from {\n                -webkit-transform: rotate(0deg);\n            }\n            to {\n                -webkit-transform: rotate(-360deg);\n            }\n        }\n        @-moz-keyframes rot {\n            from {\n                -webkit-transform: rotate(0deg);\n            }\n            to {\n                -webkit-transform: rotate(-360deg);\n            }\n        }\n        @-moz-keyframes colors {\n            0% {\n                stroke: #4285F4;\n            }\n            25% {\n                stroke: #DE3E35;\n            }\n            50% {\n                stroke: #F7C223;\n            }\n            75% {\n                stroke: #1B9A59;\n            }\n            100% {\n                stroke: #4285F4;\n            }\n        }\n\n        @-webkit-keyframes colors {\n            0% {\n                stroke: #4285F4;\n            }\n            25% {\n                stroke: #DE3E35;\n            }\n            50% {\n                stroke: #F7C223;\n            }\n            75% {\n                stroke: #1B9A59;\n            }\n            100% {\n                stroke: #4285F4;\n            }\n        }\n\n        @keyframes colors {\n            0% {\n                stroke: #4285F4;\n            }\n            25% {\n                stroke: #DE3E35;\n            }\n            50% {\n                stroke: #F7C223;\n            }\n            75% {\n                stroke: #1B9A59;\n            }\n            100% {\n                stroke: #4285F4;\n            }\n        }\n        .qp-circular-loader-path {\n            -webkit-animation-name: fillunfill, rot, colors;\n            -webkit-animation-duration: 1333ms, 5332ms, 5332ms; /* ARCTIME, 4*ARCTIME, 4*ARCTIME */\n            -webkit-animation-iteration-count: infinite, infinite, infinite;\n            -webkit-animation-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1), steps(4), linear;\n            -webkit-animation-play-state: running, running, running;\n            -webkit-animation-fill-mode: forwards;\n\n            -moz-animation-name: fillunfill, rot, colors;\n            -moz-animation-duration: 1333ms, 5332ms, 5332ms; /* ARCTIME, 4*ARCTIME, 4*ARCTIME */\n            -moz-animation-iteration-count: infinite, infinite, infinite;\n            -moz-animation-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1), steps(4), linear;\n            -moz-animation-play-state: running, running, running;\n            -moz-animation-fill-mode: forwards;\n        }\n\n    </style>\n\n    <!-- 3= STROKEWIDTH -->\n    <!-- 14= RADIUS + STROKEWIDTH/2 -->\n    <!-- 12.5= RADIUS -->\n    <!-- 1.5=  STROKEWIDTH/2 -->\n    <!-- ARCSIZE would affect the 1.5,14 part of this... 1.5,14 is specific to\n         270 degress -->\n    <g class="qp-circular-loader">\n        <path class="qp-circular-loader-path" fill="none" d="M 14,1.5 A 12.5,12.5 0 1 1 1.5,14" stroke-width="3" stroke-linecap="round"/>\n    </g>\n</svg>'),e.put("noAvailableHours.html",'<span class="angular-reservation-noAvailableHours">{{"noAvailableHours" | translate}}</span>')}]);