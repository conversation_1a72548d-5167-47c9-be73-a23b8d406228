{"name": "angular-reservation", "version": "0.6.0", "description": "AngularJS module for time reservation management. It can be use to reserve a citation on any kind of service provided in time slots", "main": "app.js", "repository": "https://github.com/hmartos/angular-reservation", "devDependencies": {"gulp": "^3.9.1", "gulp-angular-templatecache": "^1.8.0", "gulp-concat": "^2.6.0", "gulp-minify-css": "^1.2.3", "gulp-uglify": "^1.5.2", "jasmine-core": "^2.4.1", "jasmine-spec-reporter": "^2.4.0", "karma": "^0.13.22", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-jasmine": "^0.3.8", "karma-junit-reporter": "^0.4.2", "karma-spec-reporter": "0.0.26", "protractor": "^4.0.9"}, "scripts": {"pretest": "npm install", "test": "karma start karma.conf.js", "test-single-run": "karma start karma.conf.js --single-run"}, "keywords": ["reservation", "booking", "citation", "appointment", "time", "slot"]}