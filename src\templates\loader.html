<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1" width="50px" height="50px" viewBox="0 0 28 28">
    <!-- 28= RADIUS*2 + STROKEWIDTH -->

    <title>Material design circular activity spinner with CSS3 animation</title>
    <style type="text/css">
        /**************************/
        /* STYLES FOR THE SPINNER */
        /**************************/

        /*
         * Constants:
         *      RADIUS      = 12.5
         *      STROKEWIDTH = 3
         *      ARCSIZE     = 270 degrees (amount of circle the arc takes up)
         *      ARCTIME     = 1333ms (time it takes to expand and contract arc)
         *      ARCSTARTROT = 216 degrees (how much the start location of the arc
         *                                should rotate each time, 216 gives us a
         *                                5 pointed star shape (it's 360/5 * 2).
         *                                For a 7 pointed star, we might do
         *                                360/7 * 3 = 154.286)
         *
         *      SHRINK_TIME = 400ms
         */

        .qp-circular-loader {
            width:28px;  /* 2*RADIUS + STROKEWIDTH */
            height:28px; /* 2*RADIUS + STROKEWIDTH */
        }
        .qp-circular-loader-path {
            stroke-dasharray: 58.9;  /* 2*RADIUS*PI * ARCSIZE/360 */
            stroke-dashoffset: 58.9; /* 2*RADIUS*PI * ARCSIZE/360 */
            /* hides things initially */
        }

        /* SVG elements seem to have a different default origin */
        .qp-circular-loader, .qp-circular-loader * {
            -webkit-transform-origin: 50% 50%;
            -moz-transform-origin: 50% 50%;
        }

        /* Rotating the whole thing */
        @-webkit-keyframes rotate {
            from {-webkit-transform: rotate(0deg);}
            to {-webkit-transform: rotate(360deg);}
        }
        @-moz-keyframes rotate {
            from {-webkit-transform: rotate(0deg);}
            to {-webkit-transform: rotate(360deg);}
        }
        .qp-circular-loader {
            -webkit-animation-name: rotate;
            -webkit-animation-duration: 1568.63ms; /* 360 * ARCTIME / (ARCSTARTROT + (360-ARCSIZE)) */
            -webkit-animation-iteration-count: infinite;
            -webkit-animation-timing-function: linear;
            -moz-animation-name: rotate;
            -moz-animation-duration: 1568.63ms; /* 360 * ARCTIME / (ARCSTARTROT + (360-ARCSIZE)) */
            -moz-animation-iteration-count: infinite;
            -moz-animation-timing-function: linear;
        }

        /* Filling and unfilling the arc */
        @-webkit-keyframes fillunfill {
            from {
                stroke-dashoffset: 58.8 /* 2*RADIUS*PI * ARCSIZE/360 - 0.1 */
                /* 0.1 a bit of a magic constant here */
            }
            50% {
                stroke-dashoffset: 0;
            }
            to {
                stroke-dashoffset: -58.4 /* -(2*RADIUS*PI * ARCSIZE/360 - 0.5) */
                /* 0.5 a bit of a magic constant here */
            }
        }
        @-moz-keyframes fillunfill {
            from {
                stroke-dashoffset: 58.8 /* 2*RADIUS*PI * ARCSIZE/360 - 0.1 */
                /* 0.1 a bit of a magic constant here */
            }
            50% {
                stroke-dashoffset: 0;
            }
            to {
                stroke-dashoffset: -58.4 /* -(2*RADIUS*PI * ARCSIZE/360 - 0.5) */
                /* 0.5 a bit of a magic constant here */
            }
        }
        @-webkit-keyframes rot {
            from {
                -webkit-transform: rotate(0deg);
            }
            to {
                -webkit-transform: rotate(-360deg);
            }
        }
        @-moz-keyframes rot {
            from {
                -webkit-transform: rotate(0deg);
            }
            to {
                -webkit-transform: rotate(-360deg);
            }
        }
        @-moz-keyframes colors {
            0% {
                stroke: #4285F4;
            }
            25% {
                stroke: #DE3E35;
            }
            50% {
                stroke: #F7C223;
            }
            75% {
                stroke: #1B9A59;
            }
            100% {
                stroke: #4285F4;
            }
        }

        @-webkit-keyframes colors {
            0% {
                stroke: #4285F4;
            }
            25% {
                stroke: #DE3E35;
            }
            50% {
                stroke: #F7C223;
            }
            75% {
                stroke: #1B9A59;
            }
            100% {
                stroke: #4285F4;
            }
        }

        @keyframes colors {
            0% {
                stroke: #4285F4;
            }
            25% {
                stroke: #DE3E35;
            }
            50% {
                stroke: #F7C223;
            }
            75% {
                stroke: #1B9A59;
            }
            100% {
                stroke: #4285F4;
            }
        }
        .qp-circular-loader-path {
            -webkit-animation-name: fillunfill, rot, colors;
            -webkit-animation-duration: 1333ms, 5332ms, 5332ms; /* ARCTIME, 4*ARCTIME, 4*ARCTIME */
            -webkit-animation-iteration-count: infinite, infinite, infinite;
            -webkit-animation-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1), steps(4), linear;
            -webkit-animation-play-state: running, running, running;
            -webkit-animation-fill-mode: forwards;

            -moz-animation-name: fillunfill, rot, colors;
            -moz-animation-duration: 1333ms, 5332ms, 5332ms; /* ARCTIME, 4*ARCTIME, 4*ARCTIME */
            -moz-animation-iteration-count: infinite, infinite, infinite;
            -moz-animation-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1), steps(4), linear;
            -moz-animation-play-state: running, running, running;
            -moz-animation-fill-mode: forwards;
        }

    </style>

    <!-- 3= STROKEWIDTH -->
    <!-- 14= RADIUS + STROKEWIDTH/2 -->
    <!-- 12.5= RADIUS -->
    <!-- 1.5=  STROKEWIDTH/2 -->
    <!-- ARCSIZE would affect the 1.5,14 part of this... 1.5,14 is specific to
         270 degress -->
    <g class="qp-circular-loader">
        <path class="qp-circular-loader-path" fill="none" d="M 14,1.5 A 12.5,12.5 0 1 1 1.5,14" stroke-width="3" stroke-linecap="round"/>
    </g>
</svg>