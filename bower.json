{"name": "angular-reservation", "description": "AngularJS module for time reservation management. It can be use to reserve a citation on any kind of service provided in time slots", "main": ["dist/angular-reservation.js", "dist/angular-reservation.min.css"], "dependencies": {"angular": ">=1.3.0", "angular-messages": ">=1.3.0", "angular-mocks": ">=1.3.0", "angular-translate": ">=2.0.0", "angular-bootstrap": ">=1.2.0"}, "keywords": ["reservation", "booking", "citation", "appointment", "time", "slot"], "authors": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "ignore": ["node_modules", "bower_components"]}