<div class="col-md-12 angular-reservation-clientForm">
    <div class="form-group col-md-12">
        <label class="col-md-3 control-label" for="name">{{"name" | translate}}</label>
        <div class="col-md-9">
            <div class="input-group">
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-user" aria-hidden="true" style="font-size: 14px"></span>
            </span>

                <input id="name" name="name" class="form-control" placeholder="{{'name' | translate}}" type="text" ng-model="reservationCtrl.userData.name"
                       autofocus="true" ng-pattern="/^[\w\s\-\x7f-\xff]*$/" ng-minlength="2" ng-maxlength="100" required>
            </div>

            <div class="help-block" ng-messages="reserveForm.name.$error" ng-if="reserveForm.$submitted">
                <p ng-message="minlength" class="text-danger">{{"minLength" | translate: '{minLength: "2"}'}}</p>
                <p ng-message="maxlength" class="text-danger">{{"maxLength" | translate: '{maxLength: "100"}'}}</p>
                <p ng-message="pattern" class="text-danger">{{"invalidCharacters" | translate}}</p>
                <p ng-message="required" class="text-danger">{{"required" | translate}}</p>
            </div>
        </div>
    </div>

    <div class="form-group col-md-12">
        <label class="col-md-3 control-label" for="phone">{{"phone" | translate}}</label>
        <div class="col-md-9">
            <div class="input-group">
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-earphone" aria-hidden="true" style="font-size: 14px"></span>
            </span>

                <input id="phone" name="phone" class="form-control" placeholder="{{'phone' | translate}}" type="tel" ng-model="reservationCtrl.userData.phone"
                       ng-pattern="/^[0-9]*$/" ng-minlength="5" ng-maxlength="15" required>
            </div>

            <div class="help-block" ng-messages="reserveForm.phone.$error" ng-if="reserveForm.$submitted">
                <p ng-message="minlength" class="text-danger">{{"minLength" | translate: '{minLength: "5"}'}}</p>
                <p ng-message="maxlength" class="text-danger">{{"maxLength" | translate: '{maxLength: "15"}'}}</p>
                <p ng-message="pattern" class="text-danger">{{"invalidPhone" | translate}}</p>
                <p ng-message="required" class="text-danger">{{"required" | translate}}</p>
            </div>
        </div>
    </div>

    <div class="form-group col-md-12">
        <label class="col-md-3 control-label" for="email">{{"email" | translate}}</label>
        <div class="col-md-9">
            <div class="input-group">
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-envelope" aria-hidden="true" style="font-size: 14px"></span>
            </span>

                <input id="email" name="email" class="form-control" placeholder="{{'email' | translate}}" type="text" ng-model="reservationCtrl.userData.email"
                       ng-pattern="/[\w|.|-]*@\w*\.[\w|.]*/" required>
            </div>

            <div class="help-block" ng-messages="reserveForm.email.$error" ng-if="reserveForm.$submitted">
                <p ng-message="pattern" class="text-danger">{{"invalidEmail" | translate}}</p>
                <p ng-message="required" class="text-danger">{{"required" | translate}}</p>
            </div>
        </div>
    </div>

    <div class="col-md-12">
        <button id="reserve" type="submit" name="reserve" class="btn btn-success pull-right">{{"reserve" | translate}}</button>
    </div>

    <div class="col-md-12">
        <div uib-alert class="alert-success text-center" ng-if="reservationCtrl.reservationStatus == 'SUCCESS'" style="margin-top: 1em">
            <span>Success!</span>
            <p ng-if="reservationCtrl.reservationMessage">{{reservationCtrl.reservationMessage}}</p>
        </div>

        <div uib-alert class="alertt-danger text-center" ng-if="reservationCtrl.reservationStatus == 'ERROR'" style="margin-top: 1em">
            <span>Error!</span>
            <p ng-if="reservationCtrl.reservationMessage">{{reservationCtrl.reservationMessage}}</p>
        </div>
    </div>
</div>